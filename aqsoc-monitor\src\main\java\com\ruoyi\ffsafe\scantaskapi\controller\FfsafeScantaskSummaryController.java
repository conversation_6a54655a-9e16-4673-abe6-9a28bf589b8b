package com.ruoyi.ffsafe.scantaskapi.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MinioUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.ffsafe.scantaskapi.event.ScanTaskReport;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryDetailVO;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/hostscan/tasksummary")
public class FfsafeScantaskSummaryController extends BaseController {
    @Autowired
    private IFfsafeScantaskSummaryService ffsafeScantaskSummaryService;
    @Autowired
    private ScanTaskReport scanTaskReport;
    @Autowired
    private MinioUtil minioUtil;

    /**
     * 查询非凡扫描任务汇总列表
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:list')")
    @GetMapping("/list")
    public TableDataInfo list(FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        startPage();
        List<FfsafeScantaskSummary> list = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        return getDataTable(list);
    }

    /**
     * 导出非凡扫描任务汇总列表
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:export')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        List<FfsafeScantaskSummary> list = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        ExcelUtil<FfsafeScantaskSummary> util = new ExcelUtil<FfsafeScantaskSummary>(FfsafeScantaskSummary.class);
        util.exportExcel(response, list, "非凡扫描任务汇总数据");
    }

    /**
     * 获取非凡扫描任务汇总详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryById(id));
    }

    /**
     * 新增非凡扫描任务汇总
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:add')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        return toAjax(ffsafeScantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary));
    }

    /**
     * 修改非凡扫描任务汇总
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:edit')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        return toAjax(ffsafeScantaskSummaryService.updateFfsafeScantaskSummary(ffsafeScantaskSummary));
    }

    /**
     * 删除非凡扫描任务汇总
     */
    //@PreAuthorize("@ss.hasPermi('system:summary:remove')")
    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ffsafeScantaskSummaryService.deleteFfsafeScantaskSummaryByIds(ids));
    }

    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.OTHER)
    @PostMapping("/createreport")
    public AjaxResult createReport(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary) {
        String fileName = "" + ffsafeScantaskSummary.getTaskId() + "_" + ffsafeScantaskSummary.getTaskType() + "_" + DateUtils.dateTimeNow();
        try {
            boolean bRet = scanTaskReport.createTaskReport(ffsafeScantaskSummary.getTaskId(), ffsafeScantaskSummary.getTaskType(), fileName);
            if (bRet) {
                return AjaxResult.success("报告生成中。待报告生成后进行下载!");
            }
            return AjaxResult.error("数据库错误。");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @Log(title = "非凡扫描任务汇总", businessType = BusinessType.OTHER)
    @PostMapping("/downreport")
    public AjaxResult downReport(@RequestBody FfsafeScantaskSummary ffsafeScantaskSummary) {
        if (ffsafeScantaskSummary.getReportStatus() != 2) {
            return AjaxResult.error("报告生成中， 请稍后下载!");
        }

        String fileName = ffsafeScantaskSummary.getFileName() + ".zip";
        String minioFileUrl = minioUtil.getPath(fileName);
        if (minioFileUrl == null) {
            return AjaxResult.error("下载报表出错， 请联系管理员.");
        }
        return AjaxResult.success(minioFileUrl);
    }

    /**
     * 查询主机漏扫记录详细信息列表
     * 支持任务名称和扫描目标的模糊查询，返回包含统计信息的详细数据
     */
    @GetMapping("/listWithDetails")
    public TableDataInfo listWithDetails(FfsafeScantaskSummaryQueryParam queryParam)
    {
        startPage();
        List<FfsafeScantaskSummaryDetailVO> list = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryDetailList(queryParam);
        return getDataTable(list);
    }
}
